.homework-correction {
    width: 100%;
    display: flex;
    height: 100%;
    padding: 30px;
    height: 100%;
    background: url('../../assets/imgs/teachingPlan/content_bg.png');
    background-size: cover;
    .left {
        width: 452px;
        height: 100%;
        display: flex;
        flex-direction: column;
        border-radius: 20px;
        box-shadow: 0px 0px 40px 0px rgba(106, 117, 133, 0.15);
        background: url(../../assets/imgs/teachingPlan/left_bg.png) no-repeat;
        background-size: 100% 100%;
        padding: 40px 10px 27px 40px;
        margin-right: 30px;

        .left-title {
            width: 100%;
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-right: 30px;

            img {
                &:nth-child(1) {
                    margin-right: 17px;
                }
            }
        }

        .select-wrap {
            padding-right: 30px;
            flex: 1;

            .select {
                height: 100%;
                border: 1px dashed #9c9c9c;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 10px;

                span {
                    font-size: 18px;
                    cursor: pointer;
                }

                .anticon {
                    margin-right: 8px;
                    font-size: 20px;
                    color: #5c43ff;
                    font-weight: bold;
                }
            }

        }

        .left-content-wrap {
            height: calc(100% - 72px);

            padding-right: 15px;
            display: flex;
            flex-direction: column;

            .left-content {
                flex: 1;
                overflow-y: auto;
                overflow-x: hidden;
                padding-right: 15px;

                &::-webkit-scrollbar {
                    width: 5px;
                }

                /* 设置滚动条轨道 */
                &::-webkit-scrollbar-track {
                    background: transparent;
                }

                /* 设置滚动条滑块 */
                &::-webkit-scrollbar-thumb {
                    background-color: #d1d0d0;
                    border-radius: 3px;
                }

                .top-title {
                    display: flex;
                    margin-bottom: 10px;
                    align-items: center;

                    img {
                        width: 20px;
                        height: 20px;
                        margin-right: 10px;
                    }

                    .bigTitle {
                        font-weight: 600;
                        font-size: 18px;
                        color: #2E2E2E;
                        margin-right: 0px;
                        max-width: 150px;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        margin-right: 5px;
                    }

                    .smallTitle {
                        font-weight: 400;
                        font-size: 14px;
                        color: #6C6C6C;
                        margin-right: 12px;
                        max-width: 155px;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }

                    .anticon {
                        color: #97989C;
                        font-size: 18px;
                    }
                }

                .left-list {
                    margin-bottom: 20px;

                    .ant-collapse {
                        border: none;
                        background-color: unset !important;
                        // box-shadow: 2px 2px 11px 0px rgba(224, 224, 224, 0.5);

                        .ant-collapse-item {
                            background: #FFFFFF;
                            // box-shadow: 2px 2px 11px 0px rgba(192, 192, 192, 0.5);
                            border-radius: 15px;
                            margin-bottom: 10px;

                            .ant-collapse-header {
                                height: 54px;
                                background: #FFFFFF;
                                border-radius: 15px;
                                border: 1px solid #E2E2E2;
                                padding: 14px 40px 14px 16px;

                                .ant-collapse-header-text {
                                    font-weight: 400;
                                    font-size: 14px;
                                    color: #2E2E2E;
                                }
                            }

                            .ant-collapse-content {
                                border: none;

                                .ant-collapse-content-box {
                                    padding-left: 20px;

                                    .title {
                                        font-weight: 400;
                                        font-size: 14px;
                                        color: #2E2E2E;
                                        margin-bottom: 20px;
                                    }

                                    .analysis {
                                        border-radius: 6px;
                                        border: 1px solid #D4CEFF;
                                        padding: 10px;

                                        .des {
                                            display: flex;
                                            align-items: center;
                                            margin-bottom: 10px;
                                            ;

                                            .dot {
                                                width: 6px;
                                                height: 6px;
                                                background: linear-gradient(319deg, #7A43FF 0%, #7C91FC 100%);
                                                margin-right: 5px;
                                                border-radius: 50%;
                                            }

                                            span {
                                                font-weight: 400;
                                                font-size: 14px;
                                                color: #2E2E2E;
                                            }
                                        }

                                        textarea {
                                            min-height: 120px;
                                            padding: 0 0 0 10px;
                                            &::-webkit-scrollbar {
                                                width: 5px;
                                            }

                                            /* 设置滚动条轨道 */
                                            &::-webkit-scrollbar-track {
                                                background: transparent;
                                            }

                                            /* 设置滚动条滑块 */
                                            &::-webkit-scrollbar-thumb {
                                                background-color: #d1d0d0;
                                                border-radius: 3px;
                                            }
                                        }
                                        textarea.ant-input:focus, textarea.ant-input:hover {
                                            border: none;
                                            box-shadow: none;
                                        }

                                        .ant-input:focus {
                                            box-shadow: none;
                                            border-color: #d9d9d9;
                                        }
                                    }
                                }
                            }
                        }
                    }

                }

                .bottom {
                    background: #FFFFFF;
                    box-shadow: 2px 2px 11px 0px rgba(192, 192, 192, 0.5);
                    border-radius: 15px;
                    padding: 20px;

                    .bottom-item {
                        display: flex;
                        align-items: center;
                        margin-bottom: 10px;

                        .ant-input {
                            height: 36px;
                            margin-right: 10px;
                        }
                        .ant-input:focus, .ant-input:hover, .ant-input-number:focus, .ant-input-number:hover, .ant-input-number-focused {
                            border-color: #d9d9d9;
                            box-shadow: none;
                        }

                        .ant-input-number-group-wrapper {
                            min-width: 78px;
                            max-width: 96px;
                            height: 36px;
                            margin-right: 10px;
                        }

                        .ant-input-number-wrapper {
                            height: 36px;

                            .ant-input-number {
                                height: 36px;
                                border-top-left-radius: 6px;
                                border-bottom-left-radius: 6px;
                            }

                            .ant-input-number-group-addon {
                                height: 36px;
                                border-top-right-radius: 6px;
                                border-bottom-right-radius: 6px;
                            }
                        }

                        .anticon {
                            color: #97989C;
                            font-size: 18px;
                        }
                    }

                    .add-btn {
                        margin: 10px 0 0;
                        display: flex;
                        align-items: center;
                        color: #7a53fe;
                        margin-top: 20px;
                        cursor: pointer;

                        .anticon {
                            margin-right: 5px;
                        }
                    }
                }
            }

            >button {
                width: 372px;
                height: 50px;
                background: linear-gradient(319deg, #7A43FF 0%, #7C91FC 100%);
                border-radius: 25px;
                margin-top: 25px;
                flex-shrink: 0;
                padding-right: 15px;

                span {
                    font-weight: 600;
                    font-size: 18px;
                    color: #FFFFFF;
                }
            }
        }
    }

    .right {
        width: calc(100% - 482px);
        height: 100%;
        border-radius: 20px;
        box-shadow: 0px 0px 40px 0px rgba(106, 117, 133, 0.15);
        background: url(../../assets/imgs/teachingPlan/right_bg.png) no-repeat;
        background-size: 100% 100%;
        padding: 15px;
        .empty {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            background-color: #fff;
            border-radius: 10px;

            p {
                font-weight: 600;
                font-size: 24px;
                color: #2e2e2e;
                margin-top: -50px;
            }
        }

        .loading-page {
            padding: 40px;

            .loading-header {
                display: flex;
                align-items: center;
                margin-bottom: 30px;
                font-weight: 600;
                font-size: 18px;
                color: #2e2e2e;

                img {
                    margin-right: 6px;
                }
            }

            .loading-content {
                height: 320px;
                background: url(../../assets/imgs/teachingPlan/tikuang_bg.png) no-repeat;
                background-size: 100% 100%;
                display: flex;
                align-items: center;
                font-weight: 600;
                font-size: 24px;
                color: #2e2e2e;

                .animation-content {
                    width: 352px;
                    height: 270px;
                }
            }
        }
    }
}

.chapter-modal-wrapper {
    overflow: hidden!important;
    .chapter-content {
        .ant-pagination {
            display: flex;
            justify-content: center;
        }

        .chapter-list {
            margin-top: 20px;
            margin-bottom: 20px;

            .chapter-card-checked {
                background-color: #ded9ff;
                border-color: #7a43ff;
            }

            .chapter-card-checked::after {
                opacity: 1;
                border: 10px solid #7a43ff;
                border-block-end: 10px solid transparent;
                border-inline-start: 10px solid transparent;
                border-start-end-radius: 6px;
            }

            .chapter-card {
                display: flex;
                padding: 10px;
                border: 1px dashed #e0e0e0;
                border-radius: 6px;
                cursor: pointer;
                height: 140px;

                &::after {
                    position: absolute;
                    inset-block-start: 4px;
                    inset-inline-end: 12px;
                    width: 0;
                    height: 0;
                    opacity: 1;
                    transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
                    border-block-end: 10px solid transparent;
                    border-inline-start: 10px solid transparent;
                    border-start-end-radius: 6px;
                    content: '';
                }

                img {
                    width: 150px;
                    height: 110px;
                    margin-right: 5px;
                }

                .card-content {
                    width: calc(100% - 150px);
                    font-size: 14px;
                    color: #868686;

                    p {
                        margin: 0;
                    }

                    .title {
                        color: #2e2e2e;
                        font-size: 16px;
                        font-weight: 500;
                        display: -webkit-box;
                        /* 创建伸缩盒子模型 */
                        -webkit-box-orient: vertical;
                        /* 垂直排列子元素 */
                        -webkit-line-clamp: 2;
                        /* 显示两行 */
                        overflow: hidden;
                        /* 超出隐藏 */
                        text-overflow: ellipsis;
                        /* 省略号 */
                        /* 兼容性更好可以加上下面两行 */
                        word-break: break-word;
                        white-space: normal;
                    }

                    .semester,
                    .teacher,
                    .school {
                        width: 100%;
                        white-space: nowrap;
                        /* 不换行 */
                        overflow: hidden;
                        /* 超出隐藏 */
                        text-overflow: ellipsis;
                        /* 超出部分显示省略号 */
                    }
                }
            }
        }
    }
    .ant-modal {
        height: 86%;
        padding-bottom: 0;
        .ant-modal-content {
            height: 100%;
        }
        .ant-modal-body {
            height: calc(100% - 107px);
            .ant-tabs {
                height: 100%;
                .ant-tabs-content-holder {
                    height: calc(100% - 62px);
                    .ant-tabs-content, .ant-tabs-tabpane, .chapter-content {
                        height: 100%;
                        .ant-row {
                            height: calc(100% - 83px);
                            overflow: auto;
                        }
                    }
                }
            }
        }
    }
}

.chapter-modal-tree-wrapper {
    .course-name {
        font-size: 16px;
        margin-bottom: 10px;
    }
}
