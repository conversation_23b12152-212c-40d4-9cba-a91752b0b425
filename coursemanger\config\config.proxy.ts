/*
 * @Description:
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 481-04-15 15:45:35
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2024-06-24 11:31:13
 */
// const baseUrl = 'http://ecourse.scu.edu.cn/';
// const baseUrl = 'https://zhiliao.sobeylingyun.com/';
// const baseUrl = 'https://zhiliao.sobeylingyun.com';
const baseUrl = 'https://**************/';
// const baseUrl = 'https://course.ynu.edu.cn/';
// const baseUrl = 'https://changxue.cdutcm.edu.cn/';

export default {
  '/rman': {
    target: baseUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/cvod/': {
    target: baseUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/unifiedplatform': {
    target: baseUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/bucket': {
    target: baseUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  // '/bucket-z/': {
  //   target: baseUrl,
  //   changeOrigin: true,
  // },
  '/question': {
    target: baseUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/pressplatform': {
    target: baseUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/livemanage': {
    target: baseUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/edudatacenter': {
    target: baseUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/unifiedlogin': {
    target: baseUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/classroom': {
    target: baseUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/ipingestman': {
    target: baseUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/learn': {
    target: baseUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/forumservice': {
    target: baseUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/bucket-k': {
    target: baseUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/bucket-p': {
    target: baseUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/bucket-z': {
    target: baseUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/exam-api': {
    target: baseUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/exam': {
    target: baseUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/pdfview': {
    target: baseUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/documentserver': {
    target: baseUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/sensitiveword': {
    target: baseUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/pyfa': {
    target: baseUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/third-api': {
    target: baseUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/KnowledgeGraph4py': {
    target: baseUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/pubagent': {
    target: baseUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/terminator': {
    target: baseUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/medicalcaselib': {
    target: baseUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/matheditor': {
    target: baseUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/flowbpm': {
    target: baseUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/coursepractice': {
    target: baseUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/canvas-lms-adapter':{
    target: baseUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/customization': {
    target: baseUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },

  // '/usergroup':{
  //   target: 'http://sc-pressplatform.default.*************.xip.io/',
  //   changeOrigin: true
  // },

  // '/textbooks':{
  //   target: 'http://sc-pressplatform.default.*************.xip.io/',
  //   changeOrigin: true
  // },

  // '/folder':{
  //   target: 'http://sc-pressplatform.default.*************.xip.io/',
  //   changeOrigin: true
  // },

  // '/sc-jiaocai':{
  //   target: 'http://*************:8081',
  //   // http://*************:8081/swagger-ui.html#/
  //   // target: 'http://sc-pressplatform.default.*************.xip.io',
  //   pathRewrite: { '^/sc-jiaocai': ''},
  //   changeOrigin: true
  // }
};
