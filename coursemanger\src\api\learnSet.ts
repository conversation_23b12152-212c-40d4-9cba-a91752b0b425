import HTTP from './index';
// 查询设置
export function getSeting(code: string) {
  return HTTP.get(`/learn/v1/teaching/course/get/course/seting?typeId=${code}`)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}
// 修改设置
export function updateSeting(code: string, data: any) {
  return HTTP.post(
    `/learn/v1/teaching/course/update/course/seting?typeId=${code}`,
    data,
  )
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function getCertification(courseId: string) {
  return HTTP.get(`/learn/v1/certificate/info/${courseId}`)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function getCertificationSetting(courseId: string) {
  return HTTP.get(`/learn/v1/certificate/certificate/setting/info/${courseId}`)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function updateCertificationSetting(data: any) {
  return HTTP.post(`/learn/v1/certificate/certificate/setting`, data)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function queryCourseManager(courseId: string) {
  return HTTP.get(`/learn/v1/teaching/responsible/info`, {
    params: { courseId },
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function queryCourseDownload(courseId: string) {
  return HTTP.get(`/learn/v1/teaching/course/get/chapter/contents/download`, {
    params: { courseId },
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function updateCourseDownload(data: any) {
  return HTTP.post(`/learn/v1/teaching/course/update/chapter/download`, data)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export const reqPreviewImage = (data: any) => {
  return HTTP.post(`/learn/v1/certificate/info/images`, data, { responseType: 'blob' })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
};

export const reqImportCourse = (data: any) => {
  return HTTP.post(`/learn/v1/teaching/course/copy/import`, data) 
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
};
