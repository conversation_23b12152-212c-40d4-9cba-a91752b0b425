import HTTP from './index';

//登录
export function login(loginName: string, password: string) {
  return HTTP.get(
    `/rman/v1/account/login?loginName=${loginName}&password=${password}`,
  ).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  });
}

//获取用户信息
export function userInfo() {
  // return HTTP.get(`/rman/v1/account/login-userinfo/S1`)
  return HTTP.get(`/unifiedplatform/v1/user/current`)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
}

//获取教师信息
export function teacherInfo() {
  return HTTP.get(`/rman/v1/role/user?roleCode=r_teacher`)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
}
//获取资源目录树信息
export function gettreebylevel(gettreebylevel: number = 2) {
  return HTTP.get(`/rman/v1/folder/all/tree?level=${gettreebylevel}`)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
}
//获取资源目录树信息
export function onloadTreeLeaf(path: string, isOwner?: any) {
  return HTTP.get(
    `/rman/v1/folder/children?folderPath=${encodeURIComponent(
      path,
    )}%2F&isChildCount=true${isOwner ? '&isOwner=true' : ''}`,
  )
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
}

//获取元数据
export function metaData(env?: boolean) {
  return HTTP.get(
    `/rman/v1/metadata/config/fields?EntityType=biz_sobey_course&Type=basic&ResourceType=model_sobey_object_entity${
      env ? '&isTree=true' : ''
    }`,
  )
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
}

//查询课程
export function courseList(data: any) {
  return HTTP.post(`/learn/v1/course/release`, data)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
}
export function getCourseList(data: MicroCourse.searchParams & { finishTime?: string,startTime?: string }) {
  const {
    keyword,
    publishStatus,
    classificationId,
    approvalStatus,
    teacher,
    subjectId,
    startUpdateTime,
    endUpdateTime,
    page,
    size,
    courseType,
    startTime,
    finishTime
  } = data;
  return HTTP.post(`/learn/v1/course/release`, {
    keyword,
    publishStatus,
    classificationId,
    approvalStatus,
    subjectId,
    teacher,
    startUpdateTime,
    endUpdateTime,
    startTime,
    finishTime,
    isTop: null,
    courseType: courseType ? courseType : 0,
    // college: [],
    // professionIds: [],
    order: [
      {
        field: 'createDate_',
        isDesc: true,
      },
    ],
    page,
    size: size ? size : 12,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
}
export function getCourseListNew(data: MicroCourse.searchParams) {
  const {
    keyword,
    publishStatus,
    classificationId,
    approvalStatus,
    teacher,
    subjectId,
    startUpdateTime,
    endUpdateTime,
    page,
    size,
    courseType,
  } = data;
  return HTTP.post(`/learn/v1/course/bpm/release?courseState=0&isCreate=true`, {
    keyword,
    publishStatus,
    classificationId,
    approvalStatus,
    subjectId,
    teacher,
    startUpdateTime,
    endUpdateTime,
    isTop: null,
    courseType: courseType ? courseType : 0,
    // college: [],
    // professionIds: [],
    order: [
      {
        field: 'createDate_',
        isDesc: true,
      },
    ],
    page,
    size: size ? size : 12,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
}

//删除课程
export function deleteCourse(MyData: string[]) {
  return HTTP.post(`/learn/v1/course/delete`, MyData)
    .then(res => {
      if (res.status === 200) {
        return res;
      }
    })
    .catch(error => {
      return error;
    });
}

//获取删除课程进度
export function deleteCourseProcess(processId: string) {
  return HTTP.get(`/learn/v1/hive/resource/process`, {
    params: {
      processId,
    },
  })
    .then(res => {
      if (res.status === 200) {
        return res;
      }
    })
    .catch(error => {
      return error;
    });
}

//发布课程
export function publishCourse(data: any, status?: number, obj: any = {}) {
  return HTTP(`/learn/v1/course/status`, {
    method: 'POST',
    params: { status: status != null ? status : 1 },
    data: JSON.stringify({
      ...obj,
      courseId: data,
    }),
  })
    .then(res => {
      if (res.status === 200) {
        return res;
      }
    })
    .catch(error => {
      return error;
    });
}
// /map/course/pass  审核通过
export function coursePass(data: any, status?: number, obj: any = {}) {
  return HTTP(`/learn/v1/teaching/map/course/pass`, {
    method: 'POST',
    params: { status: status != null ? status : 1 },
    data: data,
  })
    .then(res => {
      if (res.status === 200) {
        return res;
      }
    })
    .catch(error => {
      return error;
    });
}
// /map/course/pass  审核通过
export function courseOverrule(data: any, params: any = {}) {
  return HTTP(`/learn/v1/teaching/course/overrule`, {
    method: 'POST',
    params: { status: 0, ...params },
    data: data,
  })
    .then(res => {
      if (res.status === 200) {
        return res;
      }
    })
    .catch(error => {
      return error;
    });
}
//发布课程
export function publishCourseNew(data: any, status?: number, obj: any = {}) {
  return HTTP(`/learn/v1/course/status/flowbpm`, {
    method: 'POST',
    params: { status: status != null ? status : 1, tag: 0, withdrawalFlag: 0 },
    data: JSON.stringify({
      ...obj,
      courseId: data,
    }),
  })
    .then(res => {
      if (res.status === 200) {
        return res;
      }
    })
    .catch(error => {
      return error;
    });
}

//下架课程
export function unPublishCourse(data: any, params: any = {}, obj: any = {}) {
  return HTTP(`/learn/v1/course/status`, {
    method: 'POST',
    params: { status: 0, ...params },
    data: JSON.stringify({
      ...obj,
      courseId: data,
    }),
  })
    .then(res => {
      if (res.status === 200) {
        return res;
      }
    })
    .catch(error => {
      return error;
    });
}
//撤回课程
export function unPublishCourseNew(data: any, params: any = {}, obj: any = {}) {
  return HTTP(`/learn/v1/teaching/course/cancel`, {
    method: 'POST',
    params: { status: 0, ...params },
    data: data,
  })
    .then(res => {
      if (res.status === 200) {
        return res;
      }
    })
    .catch(error => {
      return error;
    });
}
//置顶课程 无
// export function topCourse(id: any) {
//   return HTTP.post(`/cvod/v1/course/top?isTop=true`, id)
//     .then(res => {
//       if (res.status === 200) {
//         return res;
//       }
//     })
//     .catch(error => {
//       return error;
//     });
// }

//取消置顶课程 无
// export function unTopCourse(id: any) {
//   return HTTP.post(`/cvod/v1/course/top?&isTop=false`, id)
//     .then(res => {
//       if (res.status === 200) {
//         return res;
//       }
//     })
//     .catch(error => {
//       return error;
//     });
// }

//获取权限
export function getUserJurisdiction() {
  return HTTP.get(`/unifiedplatform/v1/user/roleapp`)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
}

export const getUserJurisdictionV2 = () =>
  HTTP(`/unifiedplatform/v1/user/rolemodule`)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
export const getPagingData = (data: string) => {
  //获取分页元数据
  return HTTP.get(`/unifiedplatform/v1/base/data/database/source/data?${data}`)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
};

/**
 * 查询课程模板数据
 * @param params
 * @param isPublish
 */
export const queryCourseTpl = (params: any) =>
  HTTP(`/learn/v1/curriculum/center/course/template/catalogue/all`, {
    method: 'POST',
    data: JSON.stringify(params),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

/**
 * 查询课堂回看数据
 * @param params
 */
export const queryCourseReview = (params: any) =>
  HTTP(`/learn/v1/course/record/course/private/authority/course`, {
    method: 'GET',
    params: params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
/**
 * 删除课堂回看数据
 * @param params
 */
export const courseReviewDel = (data: any) =>
  HTTP('/learn/v1/course/recording/delete', {
    method: 'POST',
    data: JSON.stringify(data),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
/**
 * 查询校区
 * @param params
 */
export const fetchCampus = (params: any) =>
  // HTTP(`/cvod/v1/record/course/custom/course/position`, {
  HTTP(`/learn/v1/course/custom/course/position`, {
    method: 'GET',
    params: params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

/**
 * 查询教学楼
 * @param params
 */
export const fetchAcademicBuilding = (params: any) =>
  HTTP(`/learn/v1/course/custom/course/position`, {
    method: 'GET',
    params: params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

/**
 * 查询教室
 * @param params
 */
export const fetchClassrooms = (params: any) =>
  HTTP(`/learn/v1/course/custom/course/position`, {
    method: 'GET',
    params: params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

/**
 * 查询所有学年和学期
 */
export const fetchSchoolYears = () =>
  HTTP('/learn/v1/course/all/years')
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

/**
 * 单个更新权限
 * @param params
 */
export const updateAuthoritySingle = (params: any) =>
  HTTP(`/learn/v1/course/unification/setting`, {
    method: 'POST',
    data: JSON.stringify(params),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
/**
 * 批量更新权限
 * @param params
 */
export const updateAuthorityBatch = (courseIds: any, params: any) =>
  HTTP(`/learn/v1/course/bulk/update/authority`, {
    method: 'POST',
    params,
    data: courseIds,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
/**
 * 查询学生选课信息(该课程的本班同学)
 * @param params
 */
export const selectClassmates = (params: any) =>
  HTTP(`/learn/v1/course/record/course/user/info`, {
    method: 'GET',
    params: params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
/**
 * 批量新增权限(设置本班同学)
 * @param params
 */
// export const insertCourse = (data: any) =>
//   HTTP(`/cvod/v1/record/course/insert/authority`, {
//     method: 'POST',
//     data: data,
//   })
//     .then(res => {
//       if (res.status === 200) {
//         return res.data;
//       }
//     })
//     .catch(error => {
//       return error;
//     });
// /**
//  * (同步本班同学)
//  * @param params
//  */
// export const studentsSynchronization = (data:any) =>
// HTTP(`/cvod/v1/record/course/insert`, {
//     method: 'POST',
//     params:data
//   })
//   .then((res) => {
//     if (res.status === 200) {
//       return res.data;
//     }
//   })
//   .catch((error) => {
//     return error;
//   });

/**
 * (查询本班同学)-重构接口/cvod/v1/record/course/select/user/info
 * @param params
 */
export const fetchClassmates = (data: any) =>
  HTTP(`/learn/v1/course/classmates`, {
    method: 'GET',
    params: data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
/**
 * (查询课程回看信息)-重构接口/cvod/v1/record/course/select/user/info
 * @param params
 */
export const fetchCourseReview = (courseId: string) =>
  HTTP(`/learn/v1/course/recording/video/info`, {
    method: 'GET',
    params: { courseId },
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
/**
 * (设置课程回看 显示隐藏)
 * @param params
 */
export const updateCourseReviewShow = (courseId: string, show: boolean) =>
  HTTP(`/learn/v1/course/course/back/show`, {
    method: 'GET',
    params: { courseId, show },
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

export const uploadFile = (file: any) =>
  HTTP(`/learn/v1/upload/fileupload`, {
    method: 'POST',
    data: file,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

export const getSemester = () =>
  HTTP.get('/learn/v1/dropdownbox/semester/manage/list')
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

export const getSemesters = () =>
  HTTP.get('/learn/v1/dropdownbox/semester/manage/current')
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
export const getCoverList = () =>
  HTTP.get('/learn/v1/course/cover')
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

export const updateMark = (data: any) =>
  HTTP('/learn/v1/course/recording/video/watermark', {
    method: 'POST',
    data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

export const queryColleges = () =>
  HTTP('/learn/courseSearch', {
    method: 'GET',
    params: { isHome: false },
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

export const queryIndustryCategoryList = () =>
  HTTP('/learn/static/json/industryCategories.json', {
    method: 'GET',
  })

export const pushCourses = (type: string, params: any, data: any) =>
  HTTP(`/learn/v1/course/push/${type}`, {
    method: 'POST',
    data,
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

/**
 * 校内认证
 * @param certificationType 认证类型
 * @param courseId 课程id
 */
export const reqAddAuthentication = (data: any) =>
  HTTP('/learn/v1/teaching/course/certification', {
    method: 'POST',
    data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

/**
 * 取消校内认证
 * @param courseIds 课程id
 */
export const reqCancelAuthentication = (data: any) =>
  HTTP('/learn/v1/teaching/course/certification/cancel', {
    method: 'POST',
    data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

/**
 * 复制课程
 * @param courseId 课程id
 * @param courseType 课程类型
 */
export const reqCourseCopy = (
  courseId: any,
  courseType: any,
  courseSemester?: number,
) =>
  HTTP(
    `/learn/v1/teaching/course/copy?courseId=${courseId}&courseType=${courseType}&courseSemester=${courseSemester ??
      1}`,
    {
      method: 'POST',
    },
  )
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

/**
 * (添加本班同学)
 * @param params
 */
// export const addStudents = (data: any) =>
//   HTTP(`/cvod/v1/record/course/custom/insert/authority`, {
//     method: 'POST',
//     data: data,
//   })
//     .then(res => {
//       if (res.status === 200) {
//         return res.data;
//       }
//     })
//     .catch(error => {
//       return error;
//     });
/**
 * (单个删除本班同学)
 * @param params
 */
// export const deleteStudentSingle = (data: any) =>
//   HTTP(`/cvod/v1/record/course/delete`, {
//     method: 'DELETE',
//     params: data,
//   })
//     .then(res => {
//       if (res.status === 200) {
//         return res.data;
//       }
//     })
//     .catch(error => {
//       return error;
//     });
/**
 * (批量删除本班同学)
 * @param params
 */
// export const deleteStudentBatch = (courseId: string, userCode: any) =>
//   HTTP(`/cvod/v1/record/course/delete/authorities?courseId=${courseId}`, {
//     method: 'DELETE',
//     data: userCode,
//   })
//     .then(res => {
//       if (res.status === 200) {
//         return res.data;
//       }
//     })
//     .catch(error => {
//       return error;
//     });

/**
 * 访问权限认证
 * @param courseId 课程id
 */
export const authorityIdentification = (courseId: any) =>
  HTTP(`/learn/v1/teaching/course/permissions?courseId=${courseId}`, {
    method: 'POST',
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

export const queryCourseReviewSetting = (params: any) =>
  HTTP.get('/learn/v1/course/unification/setting/info', { params })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

export const queryInvitationCode = (courseId: string) =>
  HTTP.get(`/learn/course/invitation/get/${courseId}`)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

export const queryResourceLabel = (ids: string[]) =>
  HTTP.post(`/learn/v1/homepage/resource/info`, ids)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

export const querySensitiveWord = (detectionText: string) =>
  HTTP.post(`/sensitiveword/all/match`, { detectionText })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

export const changeCover = (data: any) =>
  HTTP.post(`/learn/v1/course/cover/images`, data)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
export const reqCoverSetting = (params: any) =>
  HTTP.get(`/learn/v1/course/cover/images`, { params })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
export const storageConfig = (params: any) => {
  return HTTP('/rman/v1/course/resource/area/path', {
    method: 'POST',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
};
export const uploadImport = (data: any) => {
  return HTTP('/rman/v1/course/resource/area/import', {
    method: 'POST',
    data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
};
export const filemerge = (guid: string, fileName: string, fileGuid: string) => {
  return HTTP('/rman/v1/upload/filemerge', {
    method: 'POST',
    data: {
      guid,
      fileName,
      fileGuid,
    },
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
};
export const filesave = (data: any) => {
  return HTTP('/rman/v1/upload/filesave', {
    method: 'POST',
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
};

/**
 * 查询合并进度
 * @param fileGuid
 */
export const fetchMergeStatus = (fileGuid: string) =>
  HTTP(`/rman/v1/upload/get/composite/task/details/${fileGuid}`)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

export const getAllFieldsByType = (type: string) => {
  return HTTP(
    `/rman/v1/metadata/config/fields/upload?EntityType=${type}&Type=basic&ResourceType=model_sobey_object_entity`,
  )
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
};
export const loadChild = (path: string, isOwner?: any) => {
  return HTTP(
    `/rman/v1/folder/children?folderPath=${encodeURIComponent(
      path,
    )}%2F&isChildCount=true`,
    {
      method: 'GET',
    },
  );
};
export const newpublicfolder = (data: any) => {
  //新建公共文件夹
  return HTTP('/rman/v1/folder/public', {
    method: 'POST',
    data: data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
};
export const downloadentity = (data: Array<string>) => {
  //文件下载
  return HTTP(`/rman/v1/entity/download/fileinfo`, {
    method: 'POST',
    data: data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
};
//素材操作行为统计：like（点赞）、 collect（收藏）、download（下载）、share（分享）、copy（复制）、micr（微课）、knowledge（知识点绑定）
export const resourceOptionsCount = (
  contentId: any,
  behavioralType: string,
  obj: any,
) => {
  const temp = Array.isArray(contentId) ? contentId : [contentId];
  return HTTP(`/rman/v1/behavioral/add/batch`, {
    method: 'POST',
    params: {
      behavioralType,
      ...obj,
    },
    data: temp,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
};
export const movetree = (num?: number) => {
  return HTTP(`/rman/v1/folder/all/tree?level=${num ? num : 1}`, {
    method: 'GET',
  });
};
export const movetreeChildern = (
  path: string,
  courseId: string,
  isOwner?: any,
) => {
  return HTTP(
    `/rman/v1/folder/children?folderPath=${encodeURIComponent(
      path,
    )}%2F&isChildCount=true&courseId=${courseId}${
      isOwner ? '&isOwner=true' : ''
    }`,
    {
      method: 'GET',
    },
  )
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
};
//移动
export const entityMove = (data: copyandmoveTypes.ImoveData) => {
  return HTTP('/rman/v1/entity/move', {
    method: 'POST',
    data: data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
};
//复制
export const entityCopy = (data: copyandmoveTypes.IcopyData, isShare?: any) => {
  return HTTP(`/rman/v1/entity/copy${isShare === 1 ? '?isShare=true' : ''}`, {
    method: 'POST',
    data: data,
  });
};

//添加课程资源
export const addCourseSource = (data: [], contentId: string) => {
  return HTTP(`/learn/v1/teaching/course/add/newresource/${contentId}`, {
    method: 'POST',
    data: data,
  });
};

//共享
export const entityShare = (data: any) => {
  return HTTP(`/rman/v1/share/entity`, {
    method: 'POST',
    data: data,
  });
};
//取消共享
export const entityShareCancel = (contentId: any) => {
  return HTTP(`/rman/v1/share/cancel/entity`, {
    method: 'POST',
    params: {
      contentId,
    },
  });
};
//获取删除进度
export const deleteResult = (processid: string) => {
  return HTTP(`/rman/v1/recycle/delete/process?processid=${processid}`)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
};
export const renamefolder = (data: any) => {
  //文件夹重命名
  return HTTP('/rman/v1/folder/resource/rename', {
    method: 'POST',
    data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
};
export const renameentity = (data: any, isPublic?: boolean) => {
  //文件重命名
  return HTTP('/rman/v1/entity/entitydata/rename', {
    method: 'PATCH',
    data,
    params: {
      isPublic: isPublic,
    },
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
};
export const deleteResource = (data: Array<string>, isPublic?: boolean) => {
  return HTTP('/rman/v1/recycle/delete', {
    method: 'POST',
    params: {
      isPublic: isPublic,
    },
    data: data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
};
//删除回收站资源
export const deleteResourceRecycle = (data: Array<string>) => {
  return HTTP('/recycle/post/delete', {
    method: 'POST',
    data: data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
};
//回收站数据还原
export const reductionResource = (data: any) => {
  return HTTP('/rman/v1/recycle/restore', {
    method: 'POST',
    data: data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
};
//同步删除的视频数据
export const syncDeleteData = (data: any) => {
  return HTTP(`/learn/v1/course/recording/video/delete`, {
    method: 'POST',
    data: data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
};
//还原已删除的视频数据
export const syncReductionData = (data: any) => {
  return HTTP(`/learn/v1/course/recording/video/reduction`, {
    method: 'POST',
    data: data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
};
export const addHyperlink = (data: any) => {
  return HTTP('/rman/v1/course/resource/area/hyperlink', {
    method: 'POST',
    data: data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
};
export const courseDataOrder = (data: any) => {
  return HTTP('/rman/v1/course/resource/area/order', {
    method: 'POST',
    data: data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
};
export const downloadSetting = (data: any) => {
  return HTTP(`/rman/v1/course/resource/area/download`, {
    method: 'POST',
    data: data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
};
export const hideResource = (data: any) => {
  return HTTP(`/rman/v1/course/resource/area/hide`, {
    method: 'POST',
    data: data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
};
export const resourceAdd = (data: any) => {
  return HTTP(`/learn/v1/statistics/course/resource/add`, {
    method: 'POST',
    data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
};
export const resourceRrowsing = (data: any) => {
  return HTTP(`/learn/v1/statistics/course/resource/browsing`, {
    method: 'POST',
    data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
};
//获取教师信息
export const hyperlinkAdd = (params: any) => {
  return HTTP(`/rman/v1/course/resource/area/hyperlink/hit`, {
    method: 'post',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
};

// 查询线下学习人员列表
export const getOfflineStudentList = (params: any) => {
  return HTTP(`/learn/course/check/query/page`, {
    method: 'get',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
};
// 新增添加水印和版权配置
export const batchAddWatermark = (data: any) => {
  return HTTP('/learn/v1/course/batch/video/watermark', {
    method: 'POST',
    data: data,
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  })
    .catch(error => {
      return error;
    });
};

//查询全局添加水印和版权配置
export const getCopyrightInfo = () => {
  return HTTP(
    `/learn/v1/course/watermark/copyright/info`,{
      method: 'GET',
    }
  ).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  })
    .catch(error => {
      return error;
    });
};
// 线下人员签到
export const offlinecheckIn = (params: any) => {
  return HTTP(`/learn/course/check/add`, {
    method: 'post',
    data: params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
};


export const bindMapCourse = (params: {
  courseName: string;
  courseId: string;
  courseSemester: string;
  teacherNames: string;
  semester_teaching_courses: string;
  canvas_course_id: string;
}) => {
  return HTTP('/learn/v1/teaching/bind/map/course', {
    method: 'GET',
    params,
  });
};
//zip解压
export function HandleZip(data: any) {
  return HTTP('/rman/v1/upload/folder/unzip',{
    method: 'POST',
    data
  })
}
//查询zip解压任务
export function handleZipTask(data: any) {
  return HTTP(`/rman/v1/upload/folder/unzip/task?taskId=${data}`, {
    method: 'GET',
  })
}
//文件夹上传
export function folderImport (data: any) {
  return HTTP('/rman/v1/upload/folder/import', {
    method: 'POST',
    data
  })
}
//查询文件夹打包上传入库任务
export function getFolderImport (data: any) {
  return HTTP(`/rman/v1/upload/folder/import/task?taskId=${data}`, {
    method: 'GET',
  })
}
