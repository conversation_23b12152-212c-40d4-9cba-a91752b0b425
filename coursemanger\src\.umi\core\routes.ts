// @ts-nocheck
import React from 'react';
import { ApplyPluginsType, dynamic } from 'D:/公司项目/云上川大/sc-learn-web/coursemanger/node_modules/@umijs/runtime';
import * as umiExports from './umiExports';
import { plugin } from './plugin';
import LoadingComponent from '@/components/loading/loading';

export function getRoutes() {
  const routes = [
  {
    "path": "/",
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'layout__guideLayout__index' */'@/layout/guideLayout/index'), loading: LoadingComponent}),
    "routes": [
      {
        "path": "/",
        "redirect": "/course",
        "exact": true
      },
      {
        "path": "/coursemap",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Coursemaptool__Mapv4__perviewmap' */'@/pages/Coursemaptool/Mapv4/perviewmap'), loading: LoadingComponent}),
        "exact": true
      },
      {
        "path": "/syllabus",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__SyllabusDisplay' */'@/pages/SyllabusDisplay'), loading: LoadingComponent}),
        "exact": true
      },
      {
        "path": "/map3d",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Coursemaptool__3Dmap' */'@/pages/Coursemaptool/3Dmap'), loading: LoadingComponent}),
        "exact": true
      },
      {
        "path": "/offlinesignin",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__OfflineSignin' */'@/pages/OfflineSignin'), loading: LoadingComponent}),
        "exact": true
      },
      {
        "path": "/perviewemap",
        "title": "知识地图",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Coursemaptool__Perviewmap' */'@/pages/Coursemaptool/Perviewmap'), loading: LoadingComponent}),
        "exact": true
      },
      {
        "path": "/perviewemap2",
        "title": "知识地图",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Perviewmap2' */'@/pages/Perviewmap2'), loading: LoadingComponent}),
        "exact": true
      },
      {
        "path": "/mapv3",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Coursemaptool__Mapv3' */'@/pages/Coursemaptool/Mapv3'), loading: LoadingComponent}),
        "exact": true
      },
      {
        "path": "/othereditmap",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Coursemaptool__Othereditmap' */'@/pages/Coursemaptool/Othereditmap'), loading: LoadingComponent}),
        "exact": true
      },
      {
        "path": "/homework",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__HomeworkManagement__StudentHomeworkManagement' */'@/pages/HomeworkManagement/StudentHomeworkManagement'), loading: LoadingComponent}),
        "exact": true
      },
      {
        "path": "/coursepractice",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__CoursePractice' */'@/pages/CoursePractice'), loading: LoadingComponent}),
        "title": "课程",
        "exact": true
      },
      {
        "path": "/courseData",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__CourseData' */'@/pages/CourseData'), loading: LoadingComponent}),
        "title": "课程",
        "exact": true
      },
      {
        "path": "/home",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__HomePage' */'@/pages/HomePage'), loading: LoadingComponent}),
        "title": "主页",
        "exact": true
      },
      {
        "path": "/newcoursemap",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Coursemaptool__Teacherpreview' */'@/pages/Coursemaptool/Teacherpreview'), loading: LoadingComponent}),
        "exact": true
      },
      {
        "path": "/stuTextbook",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__TeachingAssistant__StudentTextBook' */'@/pages/TeachingAssistant/StudentTextBook'), loading: LoadingComponent}),
        "exact": true
      },
      {
        "path": "/agent",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Agent' */'@/pages/Agent'), loading: LoadingComponent}),
        "title": "AI Agent",
        "exact": true
      },
      {
        "path": "/myQuestionnaire",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__MyQuestionnaire' */'@/pages/MyQuestionnaire'), loading: LoadingComponent}),
        "exact": true
      },
      {
        "path": "/mapCourse",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__MapCourse' */'@/pages/MapCourse'), loading: LoadingComponent}),
        "title": "图谱课-canvas",
        "exact": true
      },
      {
        "path": "/canvasmap",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'layout__canvasLayout' */'@/layout/canvasLayout'), loading: LoadingComponent}),
        "title": "Canvas图谱",
        "routes": [
          {
            "path": "/canvasmap",
            "redirect": "/canvasmap/mapdetail",
            "exact": true
          },
          {
            "title": "图谱课详情",
            "path": "/canvasmap/mapdetail",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Coursemaptool__Mapv4__perviewmap' */'@/pages/Coursemaptool/Mapv4/perviewmap'), loading: LoadingComponent}),
            "exact": true
          },
          {
            "path": "/canvasmap/studentmap",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__MapCourse__components__StudentMap' */'@/pages/MapCourse/components/StudentMap'), loading: LoadingComponent}),
            "title": "图谱课详情",
            "exact": true
          },
          {
            "path": "/canvasmap/courseqa",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__CourseQA' */'@/pages/CourseQA'), loading: LoadingComponent}),
            "title": "问学",
            "exact": true
          },
          {
            "path": "/canvasmap/courseqa/detail",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__CourseQA__detail' */'@/pages/CourseQA/detail'), loading: LoadingComponent}),
            "title": "问学详情",
            "exact": true
          },
          {
            "path": "/canvasmap/statistics",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__templateDeatil__Studentstatistics' */'@/pages/templateDeatil/Studentstatistics'), loading: LoadingComponent}),
            "title": "图谱课统计",
            "exact": true
          },
          {
            "name": "统计",
            "path": "/canvasmap/mapstatistics",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__templateDeatil__Newstatistics__atlasStatistics' */'@/pages/templateDeatil/Newstatistics/atlasStatistics.tsx'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "name": "设置",
            "path": "/canvasmap/setting",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Coursemaptool__components__Setting' */'@/pages/Coursemaptool/components/Setting'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          }
        ]
      },
      {
        "title": "大屏图谱",
        "path": "/screen/map",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Coursemaptool__Screen' */'@/pages/Coursemaptool/Screen'), loading: LoadingComponent}),
        "exact": true
      },
      {
        "title": "知识点图谱",
        "path": "/knowledge/map",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Coursemaptool__KnowledgeMap' */'@/pages/Coursemaptool/KnowledgeMap'), loading: LoadingComponent}),
        "exact": true
      },
      {
        "title": "个人学习统计",
        "path": "/personalstatistic",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__StudentPersonalStatistic' */'@/pages/StudentPersonalStatistic'), loading: LoadingComponent}),
        "exact": true
      },
      {
        "title": "机构管理",
        "path": "/organization",
        "routes": [
          {
            "path": "/organization",
            "redirect": "/organization/list",
            "exact": true
          },
          {
            "title": "列表",
            "path": "/organization/list",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__OrganizationManagement__OrganizationList' */'@/pages/OrganizationManagement/OrganizationList'), loading: LoadingComponent}),
            "exact": true
          },
          {
            "title": "管理",
            "path": "/organization/management",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__OrganizationManagement__Management' */'@/pages/OrganizationManagement/Management'), loading: LoadingComponent}),
            "exact": true
          },
          {
            "title": "充值",
            "path": "/organization/recharge",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__OrganizationManagement__Recharge' */'@/pages/OrganizationManagement/Recharge'), loading: LoadingComponent}),
            "exact": true
          }
        ]
      },
      {
        "path": "/editcourse",
        "redirect": "/pages/CourseSetting",
        "exact": true
      },
      {
        "path": "/editcourse",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'layout__baseLayout' */'@/layout/baseLayout'), loading: LoadingComponent}),
        "routes": [
          {
            "path": "/editcourse/cultureprogram",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__CultureProgram' */'@/pages/CultureProgram'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/editcourse/learningsettings",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__CourseSetting' */'@/pages/CourseSetting'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/editcourse/mooclearningsettings",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__CourseSetting' */'@/pages/CourseSetting'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/editcourse/teachingAssistant",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__TeachingAssistant' */'@/pages/TeachingAssistant'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/editcourse/moocbaseinfo",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__CourseSetting' */'@/pages/CourseSetting'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/editcourse/studentlist",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Studentlist' */'@/pages/Studentlist'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/editcourse/moocteachteam",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__CourseSetting' */'@/pages/CourseSetting'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/editcourse/moochomework",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__HomeworkManagement' */'@/pages/HomeworkManagement'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "name": "统计",
            "path": "/editcourse/moocstatistics",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__templateDeatil__Newstatistics' */'@/pages/templateDeatil/Newstatistics'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/editcourse/homework",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__HomeworkManagement' */'@/pages/HomeworkManagement'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/editcourse/micromajor/homework",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Micromajor__homework' */'@/pages/Micromajor/homework'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "name": "统计",
            "path": "/editcourse/statistics",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__templateDeatil__Newstatistics' */'@/pages/templateDeatil/Newstatistics'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "name": "统计",
            "path": "/editcourse/mapstatistics",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__templateDeatil__Newstatistics__atlasStatistics' */'@/pages/templateDeatil/Newstatistics/atlasStatistics.tsx'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "name": "统计",
            "path": "/editcourse/completion",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__templateDeatil__Completion' */'@/pages/templateDeatil/Completion'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "name": "统计",
            "path": "/editcourse/studentStatistics",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__templateDeatil__Studentstatistics' */'@/pages/templateDeatil/Studentstatistics'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "name": "统计",
            "path": "/editcourse/mooccompletion",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__templateDeatil__Completion' */'@/pages/templateDeatil/Completion'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "name": "统计",
            "path": "/editcourse/completion/detail",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__templateDeatil__Detail' */'@/pages/templateDeatil/Detail'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/editcourse/baseInfo",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__CourseSetting' */'@/pages/CourseSetting'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/editcourse/operation",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__CourseOperation' */'@/pages/CourseOperation'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/editcourse/moocoperation",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__CourseOperation' */'@/pages/CourseOperation'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/editcourse/moocChapter",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Chapter' */'@/pages/Chapter'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/editcourse/moocdata",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__CourseData' */'@/pages/CourseData'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/editcourse/moocpractice",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__CoursePractice' */'@/pages/CoursePractice'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/editcourse/chapter",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Chapter' */'@/pages/Chapter'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/editcourse/spocdata",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__CourseData' */'@/pages/CourseData'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/editcourse/spocpractice",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__CoursePractice' */'@/pages/CoursePractice'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/editcourse/coursereview",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__CourseReview' */'@/pages/CourseReview'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/editcourse/courseqa",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__CourseQA' */'@/pages/CourseQA'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/editcourse/mooccourseqa",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__CourseQA' */'@/pages/CourseQA'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/editcourse/courseqa/detail",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__CourseQA__detail' */'@/pages/CourseQA/detail'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/editcourse/mooccourseqa/detail",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__CourseQA__detail' */'@/pages/CourseQA/detail'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/editcourse/teachingteam",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__CourseSetting' */'@/pages/CourseSetting'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/editcourse/studentmanagement",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__StudentManagement' */'@/pages/StudentManagement'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/editcourse/grade",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Grade' */'@/pages/Grade'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/editcourse/grademanagement",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__GradeManagement' */'@/pages/GradeManagement'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/editcourse/coursenotice",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__CourseNotice' */'@/pages/CourseNotice'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/editcourse/mooccoursenotice",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__CourseNotice' */'@/pages/CourseNotice'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/editcourse/questionnaireManage",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__QuestionnaireManage' */'@/pages/QuestionnaireManage'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/editcourse/interactive",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Interactive__List' */'@/pages/Interactive/List'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/editcourse/statisticspeople",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Interactive__StatisticsPeople' */'@/pages/Interactive/StatisticsPeople'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/editcourse/statisticssubject",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Interactive__StatisticsSubject' */'@/pages/Interactive/StatisticsSubject'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/editcourse/moocmap",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Coursemaptool__Mapv4__perviewmap' */'@/pages/Coursemaptool/Mapv4/perviewmap'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/editcourse/spocmap",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Coursemaptool__Mapv4__perviewmap' */'@/pages/Coursemaptool/Mapv4/perviewmap'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/editcourse/coursemap",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Coursemaptool__Mapv4__perviewmap' */'@/pages/Coursemaptool/Mapv4/perviewmap'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/editcourse/newcoursemap",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Coursemaptool__Mapv4__perviewmap' */'@/pages/Coursemaptool/Mapv4/perviewmap'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "name": "微专业能力达成",
            "path": "/editcourse/micromajorachievement",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__templateDeatil__MicroMajorAchievement' */'@/pages/templateDeatil/MicroMajorAchievement'), loading: LoadingComponent}),
            "title": "微专业能力达成",
            "exact": true
          },
          {
            "name": "学生达成度详情",
            "path": "/editcourse/studentachievement",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__templateDeatil__StudentAchievementDetail' */'@/pages/templateDeatil/StudentAchievementDetail'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          }
        ]
      },
      {
        "path": "/mapdetail",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'layout__mapdetailLayout__index' */'@/layout/mapdetailLayout/index'), loading: LoadingComponent}),
        "routes": [
          {
            "exact": true,
            "path": "/mapdetail",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Coursemaptool__Mapinfo' */'@/pages/Coursemaptool/Mapinfo'), loading: LoadingComponent}),
            "title": "课程地图"
          },
          {
            "path": "/mapdetail/teachingteam",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Coursemaptool__TeachingTeam' */'@/pages/Coursemaptool/TeachingTeam'), loading: LoadingComponent}),
            "title": "课程地图",
            "exact": true
          }
        ]
      },
      {
        "path": "/mapv4",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Coursemaptool__Mapv4' */'@/pages/Coursemaptool/Mapv4'), loading: LoadingComponent}),
        "title": "图谱课",
        "exact": true
      },
      {
        "path": "/atlasmap",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'layout__atlasLayout__index' */'@/layout/atlasLayout/index'), loading: LoadingComponent}),
        "routes": [
          {
            "path": "/atlasmap/mapdetail",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Coursemaptool__Mapv4__mapdetail' */'@/pages/Coursemaptool/Mapv4/mapdetail'), loading: LoadingComponent}),
            "title": "图谱课详情",
            "exact": true
          },
          {
            "path": "/atlasmap/announcement",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Coursemaptool__Mapv4__announcement' */'@/pages/Coursemaptool/Mapv4/announcement'), loading: LoadingComponent}),
            "title": "图谱课公告",
            "exact": true
          },
          {
            "path": "/atlasmap/courseqa",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__CourseQA' */'@/pages/CourseQA'), loading: LoadingComponent}),
            "title": "图谱课问答",
            "exact": true
          },
          {
            "path": "/atlasmap/statistics",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__templateDeatil__Studentstatistics' */'@/pages/templateDeatil/Studentstatistics'), loading: LoadingComponent}),
            "title": "图谱课统计",
            "exact": true
          },
          {
            "path": "/atlasmap/teaching",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__SyllabusDisplay' */'@/pages/SyllabusDisplay'), loading: LoadingComponent}),
            "title": "图谱课教学大纲",
            "exact": true
          },
          {
            "path": "/atlasmap/homework",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__HomeworkManagement__StudentHomeworkManagement' */'@/pages/HomeworkManagement/StudentHomeworkManagement'), loading: LoadingComponent}),
            "title": "图谱课作业",
            "exact": true
          }
        ]
      },
      {
        "path": "/microhome",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Coursemaptool__microprofessional' */'@/pages/Coursemaptool/microprofessional'), loading: LoadingComponent}),
        "title": "微专业",
        "exact": true
      },
      {
        "path": "/microprofessional",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'layout__microprofessionaLayout__index' */'@/layout/microprofessionaLayout/index'), loading: LoadingComponent}),
        "routes": [
          {
            "path": "/microprofessional/mapdetail",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Coursemaptool__Mapv4__mapdetail' */'@/pages/Coursemaptool/Mapv4/mapdetail'), loading: LoadingComponent}),
            "title": "微专业详情",
            "exact": true
          },
          {
            "path": "/microprofessional/announcement",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Coursemaptool__Mapv4__announcement' */'@/pages/Coursemaptool/Mapv4/announcement'), loading: LoadingComponent}),
            "title": "微专业公告",
            "exact": true
          },
          {
            "path": "/microprofessional/courseqa",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__CourseQA' */'@/pages/CourseQA'), loading: LoadingComponent}),
            "title": "微专业问答",
            "exact": true
          },
          {
            "path": "/microprofessional/offlineqa",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Coursemaptool__microprofessional__components__OfflineQA' */'@/pages/Coursemaptool/microprofessional/components/OfflineQA'), loading: LoadingComponent}),
            "title": "微专业线下答疑",
            "exact": true
          },
          {
            "path": "/microprofessional/statistics",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Micromajor__StudentAchievement' */'@/pages/Micromajor/StudentAchievement'), loading: LoadingComponent}),
            "title": "微专业能力达成",
            "exact": true
          },
          {
            "path": "/microprofessional/teaching",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__SyllabusDisplay' */'@/pages/SyllabusDisplay'), loading: LoadingComponent}),
            "title": "微专业教学大纲",
            "exact": true
          },
          {
            "path": "/microprofessional/homework",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__HomeworkManagement__StudentHomeworkManagement' */'@/pages/HomeworkManagement/StudentHomeworkManagement'), loading: LoadingComponent}),
            "title": "微专业作业",
            "exact": true
          }
        ]
      },
      {
        "path": "/capabilitymap",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Coursemaptool__Mapv3__components__CapabilityGraph' */'@/pages/Coursemaptool/Mapv3/components/CapabilityGraph'), loading: LoadingComponent}),
        "title": "能力图谱",
        "exact": true
      },
      {
        "path": "/mapv4",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'layout__mapv4Layout__index' */'@/layout/mapv4Layout/index'), loading: LoadingComponent}),
        "routes": [
          {
            "exact": true,
            "path": "/mapv4/editmap",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Coursemaptool__Mapv4__components__Editmap' */'@/pages/Coursemaptool/Mapv4/components/Editmap'), loading: LoadingComponent}),
            "title": "课程地图"
          },
          {
            "exact": true,
            "path": "/mapv4/problemap",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Coursemaptool__Mapv4__components__Problemap' */'@/pages/Coursemaptool/Mapv4/components/Problemap'), loading: LoadingComponent}),
            "title": "课程地图"
          },
          {
            "path": "/mapv4/teachingteam",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Coursemaptool__TeachingTeam' */'@/pages/Coursemaptool/TeachingTeam'), loading: LoadingComponent}),
            "title": "权限管理",
            "exact": true
          },
          {
            "path": "/mapv4/mapdetail",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Coursemaptool__Mapinfo' */'@/pages/Coursemaptool/Mapinfo'), loading: LoadingComponent}),
            "title": "基本信息",
            "exact": true
          },
          {
            "path": "/mapv4/bind",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Coursemaptool__Bind' */'@/pages/Coursemaptool/Bind'), loading: LoadingComponent}),
            "title": "绑定课程",
            "exact": true
          }
        ]
      },
      {
        "path": "/outcourse",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'layout__outcrouselayout__index' */'@/layout/outcrouselayout/index'), loading: LoadingComponent}),
        "routes": [
          {
            "exact": true,
            "path": "/outcourse",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__MicroList' */'@/pages/MicroList'), loading: LoadingComponent}),
            "title": "微课管理"
          },
          {
            "path": "/outcourse/resource",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__AddCourse__AddCourse' */'@/pages/AddCourse/AddCourse'), loading: LoadingComponent}),
            "title": "课程编辑",
            "exact": true
          },
          {
            "path": "/outcourse/mooccourse",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__MoocTabs' */'@/pages/MoocTabs'), loading: LoadingComponent}),
            "title": "公开课管理",
            "exact": true
          },
          {
            "path": "/outcourse/spoccourse",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__SpocList' */'@/pages/SpocList'), loading: LoadingComponent}),
            "title": "班级课管理",
            "exact": true
          },
          {
            "path": "/outcourse/dockcourse/:type",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__DockCourse' */'@/pages/DockCourse'), loading: LoadingComponent}),
            "title": "爱课堂",
            "exact": true
          },
          {
            "path": "/outcourse/schoolMooc",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__DockCourse' */'@/pages/DockCourse'), loading: LoadingComponent}),
            "title": "中国大学MOOC",
            "exact": true
          },
          {
            "path": "/outcourse/rainCourse",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__DockCourse' */'@/pages/DockCourse'), loading: LoadingComponent}),
            "title": "雨课堂",
            "exact": true
          },
          {
            "path": "/outcourse/superstarCourse",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__DockCourse' */'@/pages/DockCourse'), loading: LoadingComponent}),
            "title": "超星",
            "exact": true
          },
          {
            "path": "/outcourse/pmphmoocCourse",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__DockCourse' */'@/pages/DockCourse'), loading: LoadingComponent}),
            "title": "人卫慕课",
            "exact": true
          },
          {
            "path": "/outcourse/umoocCourse",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__DockCourse' */'@/pages/DockCourse'), loading: LoadingComponent}),
            "title": "优慕课",
            "exact": true
          },
          {
            "path": "/outcourse/zhihuishuCourse",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__DockCourse' */'@/pages/DockCourse'), loading: LoadingComponent}),
            "title": "智慧树",
            "exact": true
          },
          {
            "path": "/outcourse/classreview",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__ClassReview' */'@/pages/ClassReview'), loading: LoadingComponent}),
            "title": "课堂回看",
            "exact": true
          },
          {
            "path": "/outcourse/myReview",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__MyReview' */'@/pages/MyReview'), loading: LoadingComponent}),
            "title": "我的审核",
            "exact": true
          },
          {
            "path": "/outcourse/classreview/visibleSetting",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__ClassReview__visibleSetting' */'@/pages/ClassReview/visibleSetting.tsx'), loading: LoadingComponent}),
            "exact": true
          },
          {
            "path": "/outcourse/space",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__MicroList__space' */'@/pages/MicroList/space'), loading: LoadingComponent}),
            "title": "我的教学",
            "exact": true
          },
          {
            "path": "/outcourse/liveCourse",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__LiveCourse' */'@/pages/LiveCourse'), loading: LoadingComponent}),
            "title": "直播课程信息",
            "exact": true
          },
          {
            "path": "/outcourse/schoolOnline",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__DockCourse' */'@/pages/DockCourse'), loading: LoadingComponent}),
            "title": "学堂在线",
            "exact": true
          },
          {
            "path": "/outcourse/silverLearning",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__DockCourse' */'@/pages/DockCourse'), loading: LoadingComponent}),
            "title": "学银在线",
            "exact": true
          }
        ]
      },
      {
        "path": "/course",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'layout__microLayout__index' */'@/layout/microLayout/index'), loading: LoadingComponent}),
        "routes": [
          {
            "path": "/course",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__SpocList' */'@/pages/SpocList'), loading: LoadingComponent}),
            "title": "班级课",
            "exact": true
          },
          {
            "path": "/course/recycle",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Recycle' */'@/pages/Recycle'), loading: LoadingComponent}),
            "title": "回收站",
            "exact": true
          },
          {
            "path": "/course/resource",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__AddCourse__AddCourse' */'@/pages/AddCourse/AddCourse'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/course/video",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__VideoPage' */'@/pages/VideoPage'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/course/microData",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__MicroData' */'@/pages/MicroData'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/course/videolist",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__VideoList' */'@/pages/VideoList'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "exact": true,
            "path": "/course/mooccourse",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__MoocTabs' */'@/pages/MoocTabs'), loading: LoadingComponent}),
            "title": "课程"
          },
          {
            "path": "/course/mapCourse",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__MoocTabs' */'@/pages/MoocTabs'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/course/microcourse",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__MicroList' */'@/pages/MicroList'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/course/trainingCourse",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__SpocList' */'@/pages/SpocList'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/course/minemap",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Coursemaptool__Minemap' */'@/pages/Coursemaptool/Minemap'), loading: LoadingComponent}),
            "title": "我的地图",
            "exact": true
          },
          {
            "path": "/course/map_recycle",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Coursemaptool__Recyclebin' */'@/pages/Coursemaptool/Recyclebin'), loading: LoadingComponent}),
            "title": "地图回收站",
            "exact": true
          },
          {
            "path": "/course/sharetemap",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Coursemaptool__Sharetemap' */'@/pages/Coursemaptool/Sharetemap'), loading: LoadingComponent}),
            "title": "共享地图",
            "exact": true
          },
          {
            "path": "/course/agent",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Agent__AgentInner' */'@/pages/Agent/AgentInner.tsx'), loading: LoadingComponent}),
            "title": "AI Agent",
            "exact": true
          },
          {
            "path": "/course/dockcourse/:type",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__DockCourse' */'@/pages/DockCourse'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/course/schoolMooc",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__DockCourse' */'@/pages/DockCourse'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/course/rainCourse",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__DockCourse' */'@/pages/DockCourse'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/course/superstarCourse",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__DockCourse' */'@/pages/DockCourse'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/course/pmphmoocCourse",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__DockCourse' */'@/pages/DockCourse'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/course/umoocCourse",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__DockCourse' */'@/pages/DockCourse'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/course/schoolOnline",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__DockCourse' */'@/pages/DockCourse'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/course/silverLearning",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__DockCourse' */'@/pages/DockCourse'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/course/zhihuishuCourse",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__DockCourse' */'@/pages/DockCourse'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/course/classreview",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__ClassReview' */'@/pages/ClassReview'), loading: LoadingComponent}),
            "title": "在线课堂",
            "exact": true
          },
          {
            "path": "/course/myReview",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__MyReview' */'@/pages/MyReview'), loading: LoadingComponent}),
            "title": "我的审核",
            "exact": true
          },
          {
            "path": "/course/myLive",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__MyLive' */'@/pages/MyLive'), loading: LoadingComponent}),
            "title": "我的直播",
            "exact": true
          },
          {
            "path": "/course/classreview/visibleSetting",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__ClassReview__visibleSetting' */'@/pages/ClassReview/visibleSetting.tsx'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/course/space",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__MicroList__space' */'@/pages/MicroList/space'), loading: LoadingComponent}),
            "title": "课程",
            "exact": true
          },
          {
            "path": "/course/liveCourse",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__LiveCourse' */'@/pages/LiveCourse'), loading: LoadingComponent}),
            "title": "直播课程信息",
            "exact": true
          }
        ]
      },
      {
        "path": "/out",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'layout__outLayout__outLayout' */'@/layout/outLayout/outLayout'), loading: LoadingComponent}),
        "routes": [
          {
            "exact": true,
            "path": "/out/course",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__MicroList' */'@/pages/MicroList'), loading: LoadingComponent})
          },
          {
            "exact": true,
            "path": "/out/mooccourse",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__MoocTabs' */'@/pages/MoocTabs'), loading: LoadingComponent})
          },
          {
            "exact": true,
            "path": "/out/portalconfiguration",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__BannerConfiguration' */'@/pages/BannerConfiguration'), loading: LoadingComponent})
          },
          {
            "exact": true,
            "path": "/out/classifiedconfiguration",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__ClassifiedConfiguration' */'@/pages/ClassifiedConfiguration'), loading: LoadingComponent})
          },
          {
            "path": "/out/coursereview",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__CourseReview' */'@/pages/CourseReview'), loading: LoadingComponent}),
            "exact": true
          },
          {
            "path": "/out/courseqa",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__CourseQA' */'@/pages/CourseQA'), loading: LoadingComponent}),
            "exact": true
          },
          {
            "path": "/out/courseqa/detail",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__CourseQA__detail' */'@/pages/CourseQA/detail'), loading: LoadingComponent}),
            "exact": true
          }
        ]
      },
      {
        "path": "/coursetemplate",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'layout__templateLayout__index' */'@/layout/templateLayout/index'), loading: LoadingComponent}),
        "routes": [
          {
            "exact": true,
            "path": "/coursetemplate/mytemplate",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__CourseTemplate' */'@/pages/CourseTemplate'), loading: LoadingComponent}),
            "title": "课程包"
          },
          {
            "path": "/coursetemplate/sharetemplate",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__CourseTemplate' */'@/pages/CourseTemplate'), loading: LoadingComponent}),
            "title": "课程包",
            "exact": true
          },
          {
            "path": "/coursetemplate/myReview",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__CourseTemplateReview' */'@/pages/CourseTemplateReview'), loading: LoadingComponent}),
            "title": "课程包",
            "exact": true
          },
          {
            "path": "/coursetemplate/out/mytemplate",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__CourseTemplate' */'@/pages/CourseTemplate'), loading: LoadingComponent}),
            "title": "课程包",
            "exact": true
          },
          {
            "path": "/coursetemplate/out/sharetemplate",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__CourseTemplate' */'@/pages/CourseTemplate'), loading: LoadingComponent}),
            "title": "课程包",
            "exact": true
          }
        ]
      },
      {
        "path": "/coursemap",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'layout__coursemapLayout__index' */'@/layout/coursemapLayout/index'), loading: LoadingComponent}),
        "routes": [
          {
            "exact": true,
            "path": "/coursemap/majormap",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Coursemaptool__Majormap' */'@/pages/Coursemaptool/Majormap'), loading: LoadingComponent}),
            "title": "专业地图"
          },
          {
            "exact": true,
            "path": "/coursemap/minemap",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Coursemaptool__Minemap' */'@/pages/Coursemaptool/Minemap'), loading: LoadingComponent}),
            "title": "课程地图"
          },
          {
            "path": "/coursemap/sharetemap",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Coursemaptool__Sharetemap' */'@/pages/Coursemaptool/Sharetemap'), loading: LoadingComponent}),
            "title": "课程地图",
            "exact": true
          },
          {
            "path": "/coursemap/recyclebin",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Coursemaptool__Recyclebin' */'@/pages/Coursemaptool/Recyclebin'), loading: LoadingComponent}),
            "title": "课程地图回收站",
            "exact": true
          },
          {
            "path": "/coursemap/labelsconfig",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Coursemaptool__labelsconfig' */'@/pages/Coursemaptool/labelsconfig'), loading: LoadingComponent}),
            "title": "标签配置",
            "exact": true
          }
        ]
      },
      {
        "path": "/micromajor",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'layout__micromajorLayout__index' */'@/layout/micromajorLayout/index'), loading: LoadingComponent}),
        "routes": [
          {
            "exact": true,
            "path": "/micromajor/list",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Micromajor' */'@/pages/Micromajor'), loading: LoadingComponent}),
            "title": "微专业"
          },
          {
            "exact": true,
            "path": "/micromajor/course",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Micromajor__micromajorCourse' */'@/pages/Micromajor/micromajorCourse'), loading: LoadingComponent}),
            "title": "微专业开课"
          }
        ]
      },
      {
        "path": "/editmicromajor",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'layout__editmicromajorLayout__index' */'@/layout/editmicromajorLayout/index'), loading: LoadingComponent}),
        "routes": [
          {
            "exact": true,
            "path": "/editmicromajor/pyfa",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Micromajor__components__pyfa' */'@/pages/Micromajor/components/pyfa'), loading: LoadingComponent}),
            "title": "培养方案"
          },
          {
            "exact": true,
            "path": "/editmicromajor/editmap",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Micromajor__components__editmap' */'@/pages/Micromajor/components/editmap'), loading: LoadingComponent}),
            "title": "微专业图谱"
          }
        ]
      },
      {
        "path": "/tempatedetail",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'layout__templateDeatilLayout' */'@/layout/templateDeatilLayout'), loading: LoadingComponent}),
        "routes": [
          {
            "path": "/tempatedetail/courseInfo",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__templateDeatil__CourseInfo' */'@/pages/templateDeatil/CourseInfo'), loading: LoadingComponent}),
            "title": "课程包",
            "exact": true
          },
          {
            "path": "/tempatedetail/chapter",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__templateDeatil__Chapter' */'@/pages/templateDeatil/Chapter'), loading: LoadingComponent}),
            "title": "课程包",
            "exact": true
          },
          {
            "path": "/tempatedetail/teachingteam",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__templateDeatil__TeachTeam' */'@/pages/templateDeatil/TeachTeam'), loading: LoadingComponent}),
            "title": "课程包",
            "exact": true
          },
          {
            "path": "/tempatedetail/resources",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__templateDeatil__Resources' */'@/pages/templateDeatil/Resources'), loading: LoadingComponent}),
            "title": "课程包",
            "exact": true
          },
          {
            "path": "/tempatedetail/usage",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__templateDeatil__Usage' */'@/pages/templateDeatil/Usage'), loading: LoadingComponent}),
            "title": "课程包",
            "exact": true
          },
          {
            "path": "/tempatedetail/homework",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__HomeworkManagement__HomeworkList' */'@/pages/HomeworkManagement/HomeworkList'), loading: LoadingComponent}),
            "title": "课程包",
            "exact": true
          }
        ]
      },
      {
        "path": "/tccaselibrary",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'layout__libraryLayout__index' */'@/layout/libraryLayout/index'), loading: LoadingComponent}),
        "title": "案例列表",
        "exact": true
      },
      {
        "path": "/tccaselibrary/:id",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__TeachingCase__index' */'@/pages/TeachingCase/index'), loading: LoadingComponent}),
        "title": "案例编辑",
        "exact": true
      },
      {
        "path": "/announcement",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'layout__announcementLayout__index' */'@/layout/announcementLayout/index'), loading: LoadingComponent}),
        "title": "公告",
        "exact": true
      },
      {
        "path": "/login",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__index' */'@/pages/index'), loading: LoadingComponent}),
        "exact": true
      },
      {
        "path": "/reviewcenter",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'layout__reviewcenterLayout__index' */'@/layout/reviewcenterLayout/index'), loading: LoadingComponent}),
        "routes": [
          {
            "path": "/reviewcenter/import",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__ReviewCenter__Share' */'@/pages/ReviewCenter/Share'), loading: LoadingComponent}),
            "title": "审核",
            "exact": true
          },
          {
            "path": "/reviewcenter/share",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__ReviewCenter__Share' */'@/pages/ReviewCenter/Share'), loading: LoadingComponent}),
            "title": "审核",
            "exact": true
          },
          {
            "path": "/reviewcenter/process",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__ReviewCenter__Bpmn' */'@/pages/ReviewCenter/Bpmn'), loading: LoadingComponent}),
            "title": "审核",
            "exact": true
          },
          {
            "path": "/reviewcenter/course",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__MyReview' */'@/pages/MyReview'), loading: LoadingComponent}),
            "title": "审核",
            "exact": true
          },
          {
            "path": "/reviewcenter/call",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__ReviewCenter__Call' */'@/pages/ReviewCenter/Call'), loading: LoadingComponent}),
            "title": "审核",
            "exact": true
          },
          {
            "path": "/reviewcenter/syllabus",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__ReviewCenter__Syllabus' */'@/pages/ReviewCenter/Syllabus'), loading: LoadingComponent}),
            "title": "审核",
            "exact": true
          }
        ]
      }
    ]
  }
];

  // allow user to extend routes
  plugin.applyPlugins({
    key: 'patchRoutes',
    type: ApplyPluginsType.event,
    args: { routes },
  });

  return routes;
}
