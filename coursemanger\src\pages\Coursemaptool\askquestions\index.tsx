/*
 * @Author: 李武林
 * @Date: 2022-03-22 15:57:48
 * @LastEditors: 李武林
 * @LastEditTime: 2022-04-08 16:55:52
 * @FilePath: \coursemanger\src\pages\NewCourseMap\components\askquestions\index.tsx
 * @Description:
 *
 * Copyright (c) 2022 by 李武林/索贝数码科技股份有限公司, All Rights Reserved.
 */
import QAService from '@/api/qa';
import { Button, message, Input, Form, Popconfirm, Checkbox } from 'antd';
import React from 'react';
import { FC } from 'react';
import {
  useSelector,
  useLocation
} from
  'umi';
import './index.less';
import { LeftOutlined } from '@ant-design/icons';
import { getSensitiveWord } from '@/utils';
import useLocale from '@/hooks/useLocale';
import Editor from '@/pages/CourseSetting/components/Editor';

const { TextArea } = Input;

const Askquestions: FC<any> = (props) => {
  const { t } = useLocale();
  const location: any = useLocation();
  const [addform] = Form.useForm();
  const type = location.query.type || '1';
  const courseid: any = location.query.id || props.courseid || '';
  const { userInfo } = useSelector<any, any>((state) => state.global);


  // 移除HTML标签的函数
  function removeHtmlTags(str: string) {
    return str.replace(/<[^>]+>/g, '');
  }

  const onFinish = async (values: any) => {
    // 敏感词检测时使用纯文本内容
    await checkText(values.title + removeHtmlTags(values.content || ''));
    QAService.addTopic({
      map_id: props.mapid,
      link_name: props.coursename, // 课程名称
      link_id: courseid, // 课程id
      content: values.content || '', // 提交HTML格式内容给后端
      user_id: userInfo.userCode,
      user_name: userInfo.nickName,
      user_roles: userInfo.roles,
      title: values.title,
      extend_link_id: props.selectNode.id, // 知识点id
      anonymity: values.anonymity,
      extend_message: props.selectNode.label,
      extend_url: `/learn/course/detail/${props.courseid}?mapid=${props.mapid}&nodeid=${props.selectNode.id}`,
      extend_type: props.extend_type
    }).then((res: any) => {
      if (res.error_msg == "Success") {
        message.success(t('发表成功！'));
        props.onback();
      } else {
        message.error(t('发表失败！'));
      }
    });
  };

  const onFinishFailed = (errorInfo: any) => {
    console.log('Failed:', errorInfo);
  };

  // 检测敏感词
  const checkText = async (text: string) => {
    await getSensitiveWord(text, t('问答'), () => {
      return Promise.resolve(true);
    }, () => {
      return Promise.reject(false);
    });
  };


  return (
    <div>
      <div style={{ cursor: 'pointer' }} onClick={() => props.onback()}>
        <LeftOutlined style={{ color: '#828282' }} /><span style={{ color: '#828282', marginLeft: '5px' }}>{t("返回")}</span>
      </div>
      <div style={{ display: 'inline-block' }}>
        <div className="knowledge_name">
          <div className="both_view"></div>
          <span>{t("相关知识点：")}{props.selectNode?.label}</span>
        </div>
      </div>
      <div style={{ marginTop: '20px' }}>
        <Form
          name="basic"
          layout="vertical"
          initialValues={{ remember: true }}
          onFinish={onFinish}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          form={addform}>

          <Form.Item
            label={(() => {
              return <div className="from_item_name">
                <div className="both_view"></div>
                <span>{t("标题：")}</span>
              </div>;
            })()}
            name="title"
            initialValue={""}>

            {/* <Input placeholder="请输入标题 …" showCount maxLength={30} /> */}
            <TextArea rows={1} placeholder={t("请输入标题")} showCount maxLength={30} />
          </Form.Item>
          <Form.Item
            label={(() => {
              return <div className="from_item_name">
                <div className="both_view"></div>
                <span>{t("内容：")}</span>
              </div>;
            })()}
            name="content"
            initialValue={""}>

            <div style={{ width: '100%' }}>
              <Editor height={300} name="content" onlyText wordlimit={400} />
            </div>
          </Form.Item>
          <Form.Item
            name="anonymity"
            valuePropName="checked"
            initialValue={false}
            // mooc 隐藏匿名  spoc 显示匿名
            hidden={type == '1' || type == 'mooc' ? true : false}>

            <Checkbox>{t("匿名")}</Checkbox>
          </Form.Item>
          <Form.Item>
            <Popconfirm
              title={t("确认发表吗?")}
              onConfirm={() => { addform.submit(); }}
              onCancel={() => { }}
              okText={t("确认")}
              cancelText={t("取消")}>

              {/* htmlType="submit" */}
              <Button type="primary" style={{ width: '30%', marginLeft: '35%' }}>{t("发表")}

              </Button>
            </Popconfirm>
          </Form.Item>
        </Form>
      </div>
    </div>);

};

export default Askquestions;