import HTTP from './index';
const PREFIX = '/learn';
const PYFA_PREFIX = '/pyfa';

export namespace CheckService {
  export function syllabusLast(params: CheckParams) {
    return HTTP(`${PREFIX}/v1/teaching/course/query/classes/syllabus/last`, {
      method: 'POST',
      params,
    });
  }
  export function courseLast(params: CheckParams) {
    return HTTP(`${PREFIX}/v1/teaching/course/query/course/syllabus/last`, {
      method: 'POST',
      params,
    });
  }
  export function classesLast(params: CheckParams) {
    return HTTP(`${PREFIX}/v1/teaching/course/bind/classes/syllabus/last`, {
      method: 'POST',
      params,
    });
  }
  export function syllabusChapter(data: SyllabusChapterBody) {
    return HTTP(`${PREFIX}/v1/teaching/binding/course/syllabus/chapter`, {
      method: 'POST',
      data,
    });
  }
  export function syllabusChapterContent(data: SyllabusChapterContentBody) {
    return HTTP(
      `${PREFIX}/v1/teaching/binding/course/syllabus/chapter/content`,
      {
        method: 'POST',
        data,
      },
    );
  }
  export function syllabusKnowledge(data: SyllabusKnowledgeBody, params?: any) {
    return HTTP(`${PREFIX}/v1/teaching/binding/course/syllabus/knowledge`, {
      method: 'POST',
      data,
      params,
    });
  }
  export interface SyllabusKnowledgeBody {
    coures_code: string;
    courseId: string;
    courseSemester: number;
    knowledgeGraphVOS: KnowledgeGraphVO[];
    syllabusContentId: string;
    syllabus_module_id: string;
    targetIds: string[];
  }

  export interface KnowledgeGraphVO {
    chapterType: string;
    couresCode: string;
    courseId: string;
    courseSemester: number;
    entity: string;
    id: number;
    mapId: number;
    nodeId: string;
    nodePropertyId: number;
    resourceType: number;
    syllabusContentId: string;
    syllabusModuleId: string;
  }
  export function getTableData(params: {
    courseId: string;
    courseSemester: number;
  }) {
    return HTTP(
      `${PREFIX}/v1/teaching/details/course/syllabus/${params.courseId}`,
      {
        method: 'POST',
        params,
      },
    );
  }
  export function addCourseSyllabus(data: {
    courseId: string;
    courseSemester: number;
    coures_code: string;
    coures_cversion: string;
    /** */
    applicableMajorName: string
    applicableMajorCode: string
    grade: string
    trainingPlanId?: string
  }) {
    return HTTP(`${PREFIX}/v1/teaching/course/add/course/syllabus`, {
      method: 'POST',
      data,
      params: {
        courseId: data.courseId,
      },
    });
  }
  export function teachingContent(id: string) {
    return HTTP(`${PYFA_PREFIX}/v1/course/teachingcontent`, {
      method: 'get',
      params: { id },
    });
  }
  export function learnSearch(data: {
    pageSize: number;
    pageIndex: number;
    course?: string;
    courseId?: string
  }) {
    return HTTP(`${PYFA_PREFIX}/v1/course/learn/search`, {
      method: 'post',
      data,
    });
  }
  export function infoInstalments(params: any) {
    return HTTP(`${PREFIX}/m1/knowledge/info/instalments`, {
      method: 'get',
      params,
    });
  }

  export type InstalMentsData = {
    id: number;
    mapName: string;
    mapCover: null;
    major: null;
    college: null;
    majorNames: null;
    collegeNames: null;
    courseName: null;
    subject: null;
    subjectName: null;
    createDate: string;
    userCode: string;
    isShow: number;
    teacherName: string;
    parentId: number;
    quoteUser: null;
    type: number;
    labels: string;
    isRemind: number;
    resourceId: null;
    courseNumber: null;
    grade: null;
    mapTime: null;
    courseCode: null;
    couresCversion: null;
    courseSemester: null;
  };

  export function courseGrades(courseId: string) {
    return HTTP(`${PYFA_PREFIX}/v1/course/grades`, {
      method: 'get',
      params: {
        courseId,
      },
    });
  }
  export interface SyllabusChapterContentBody {
    couresSyllabusContentUpdates: CouresSyllabusContentUpdate[];
    courseId: string;
    courseSemester: number;
    resource_id: string;
    resource_name: string;
    resource_type: string;
    map_entity_id: string;
  }

  export interface CouresSyllabusContentUpdate {
    content: string;
    id: string;
    teachingModule: string;
  }
  export interface SyllabusChapterBody {
    coures_code: string;
    courseId: string;
    courseSemester: number;
    syllabusContentId: string;
    syllabusResourceVOS: SyllabusResourceVO[];
    syllabus_module_id: string;
    targetIds: string[];
  }

  export interface SyllabusResourceVO {
    course_id: string;
    resource_id: string;
    resource_name: string;
    resource_show: boolean;
    resource_type: string;
    syllabusId: string[];
  }
  export type CheckParams = {
    courseId: string;
    course_code?: string;
    courseSemester?: string;
  };
  // 获取课程详情
  export function getCourseDetail(id: any, isBaseInfo?: Boolean, isTrainingPlan?: boolean) {
    return HTTP(`/pyfa/v1/course?courseId=${id}&isBaseInfo=${isBaseInfo}&isTrainingPlan=${isTrainingPlan}`, {
      method: 'get',
    });
  }

  export interface IBindOutline {
    /** 课程大纲课程code */
    couresCversion: string;
    /** 课程大纲唯一id */
    courseCode: string;
    /** 知识图谱id */
    id: string;
    /** 课程id */
    courseId?: string
    /** 课程期数 */
    courseSemester?: string
    /** 适用专业code */
    applicableMajorCode: string
    /** 适用专业Name */
    applicableMajorName: string
    /** 年级 */
    grade: string
    /** 培养方案id */
    trainingPlanId?: string
  }
  /**
   * 知识地图绑定大纲
   */
  export function bindOutlineByCourseCode(data: IBindOutline) {
    return HTTP(`/learn/m1/knowledge/bind/coursecode`, {
      method: 'POST',
      data,
    });
  }

  /**
   * 根据地图id查询绑定的课程大纲详情
   */
  export function getCourseDetailByMapId(
    mapId: string,
    params: {
      courseCode: string;
      courseSemester?: string;
    },
  ) {
    return HTTP(`/learn/v1/teaching/details/knowledge/syllabus/${mapId}/`, {
      method: 'POST',
      params,
    });
  }

  /**
   *
   * 在知识图谱页面绑定大纲（知识图谱里面）
   */
  export function bindKnowledgeInMap(data: any, params?: any) {
    return HTTP(`/learn/v1/teaching/binding/course/syllabus/knowledge`, {
      method: 'POST',
      data,
      params,
    });
  }
  /**
   * 知识地图知识点绑定课程大纲(在知识图谱页面)
   */
  export function bindSyllabusInMap(data: any, params: any) {
    return HTTP(`/learn/v1/teaching/binding/map/syllabus/knowledge`, {
      method: 'POST',
      data,
      params,
    });
  }

  export interface IQueryGoalProps {
    /** 课程大纲code */
    courseCode?: string
    courseId?: string
    courseSemester?: string
    mapId: string
    nodeId : string
  }
  /**
   * 根据node的id查询绑定的目标
   */
  export function queryGoalByMapNode(params: IQueryGoalProps) {
    return HTTP(`/learn/v1/associate/student/get/knowledge/node/target`, {
      method: 'POST',
      params
    })
  }

  /**
   * 
   */
  export function querySyllabusList(params: {courseId: string}) {
    return HTTP(`/pyfa/v1/course/get_training_plan_by_course`, {
      method: 'GET',
      params
    })
  }
  export function queryMicroPermission(params: {courseId: string}) {
    return HTTP(`/learn/v1/teaching/course/get/microProfession/teacher/permission`, {
      method: 'GET',
      params
    })
  }

  // 知识图谱导入大纲
  export function mapBindCourse(data: { courseId: string, mapId: string, name: string}) {
    return HTTP(`/learn/m1/knowledge/course/outline/import/map`, {
      method: 'POST',
      data
    })
  }
  export function reqThirdCourse(params: { courseNo: string; serialNo: string }) {
    return HTTP(`/pyfa/v1/thirdcourseoutline/getthirdcourseoutlinebycourseid`, {
      method: 'GET',
      params
    }).then(res => {
      if (res.status === 200) {
        return res.data
      }
    })
  }
}
